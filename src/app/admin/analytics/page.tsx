'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
  UserGroupIcon,
  DocumentTextIcon,
  CalendarIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatPrice } from '@/lib/utils';
import { useRequireAdmin } from '@/contexts/auth-context';
import { RoleBasedLayout } from '@/components/layout/role-based-layout';

interface AnalyticsData {
  revenue: {
    total: number;
    thisMonth: number;
    lastMonth: number;
    growth: number;
  };
  orders: {
    total: number;
    thisMonth: number;
    lastMonth: number;
    growth: number;
    byStatus: {
      pending: number;
      quoted: number;
      approved: number;
      inProgress: number;
      completed: number;
      cancelled: number;
    };
  };
  customers: {
    total: number;
    thisMonth: number;
    lastMonth: number;
    growth: number;
    active: number;
  };
  products: {
    total: number;
    active: number;
    featured: number;
    outOfStock: number;
  };
  topProducts: Array<{
    id: string;
    name: string;
    orders: number;
    revenue: number;
  }>;
  recentActivity: Array<{
    id: string;
    type: 'order' | 'user' | 'product';
    description: string;
    timestamp: string;
  }>;
}

export default function AdminAnalyticsPage() {
  const { user, isLoading } = useRequireAdmin();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');

  useEffect(() => {
    if (user?.role === 'admin') {
      fetchAnalytics();
    }
  }, [user, timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/analytics?range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAnalytics(data);
      } else {
        console.error('Failed to fetch analytics');
        // Mock data for demonstration
        setAnalytics({
          revenue: {
            total: 125000,
            thisMonth: 45000,
            lastMonth: 38000,
            growth: 18.4
          },
          orders: {
            total: 156,
            thisMonth: 23,
            lastMonth: 19,
            growth: 21.1,
            byStatus: {
              pending: 8,
              quoted: 5,
              approved: 3,
              inProgress: 4,
              completed: 12,
              cancelled: 1
            }
          },
          customers: {
            total: 89,
            thisMonth: 12,
            lastMonth: 8,
            growth: 50.0,
            active: 67
          },
          products: {
            total: 24,
            active: 22,
            featured: 8,
            outOfStock: 2
          },
          topProducts: [
            { id: '1', name: 'Elegant Portrait - Classic Style', orders: 15, revenue: 45000 },
            { id: '2', name: 'Glamorous Bedazzled Art', orders: 12, revenue: 36000 },
            { id: '3', name: 'Premium Family Portrait', orders: 8, revenue: 28000 }
          ],
          recentActivity: [
            { id: '1', type: 'order', description: 'New custom order from Jane Doe', timestamp: '2024-01-15T10:30:00Z' },
            { id: '2', type: 'user', description: 'New customer registration: John Smith', timestamp: '2024-01-15T09:15:00Z' },
            { id: '3', type: 'order', description: 'Order #ORD-123 marked as completed', timestamp: '2024-01-15T08:45:00Z' }
          ]
        });
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) {
      return <ArrowUpIcon className="h-4 w-4 text-green-600" />;
    } else if (growth < 0) {
      return <ArrowDownIcon className="h-4 w-4 text-red-600" />;
    }
    return null;
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-600';
    if (growth < 0) return 'text-red-600';
    return 'text-muted-foreground';
  };

  if (isLoading || loading) {
    return (
      <RoleBasedLayout requiredRole="admin">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading analytics...</p>
          </div>
        </div>
      </RoleBasedLayout>
    );
  }

  if (!analytics) {
    return (
      <RoleBasedLayout requiredRole="admin">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <p className="text-muted-foreground">Unable to load analytics. Please try again.</p>
          </div>
        </div>
      </RoleBasedLayout>
    );
  }

  return (
    <RoleBasedLayout requiredRole="admin" showRoleInfo>
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-2">
                Analytics Dashboard
              </h1>
              <p className="text-muted-foreground">
                Comprehensive insights into your business performance
              </p>
            </div>
            <div className="flex items-center gap-2">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">Last year</option>
              </select>
            </div>
          </div>
        </motion.div>

        {/* Key Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          {/* Revenue */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">Total Revenue</p>
                  <p className="text-2xl font-bold text-foreground">{formatPrice(analytics.revenue.total)}</p>
                  <div className="flex items-center gap-1 mt-1">
                    {getGrowthIcon(analytics.revenue.growth)}
                    <span className={`text-sm ${getGrowthColor(analytics.revenue.growth)}`}>
                      {Math.abs(analytics.revenue.growth)}% vs last month
                    </span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Orders */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">Total Orders</p>
                  <p className="text-2xl font-bold text-foreground">{analytics.orders.total}</p>
                  <div className="flex items-center gap-1 mt-1">
                    {getGrowthIcon(analytics.orders.growth)}
                    <span className={`text-sm ${getGrowthColor(analytics.orders.growth)}`}>
                      {Math.abs(analytics.orders.growth)}% vs last month
                    </span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <DocumentTextIcon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Customers */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">Total Customers</p>
                  <p className="text-2xl font-bold text-foreground">{analytics.customers.total}</p>
                  <div className="flex items-center gap-1 mt-1">
                    {getGrowthIcon(analytics.customers.growth)}
                    <span className={`text-sm ${getGrowthColor(analytics.customers.growth)}`}>
                      {Math.abs(analytics.customers.growth)}% vs last month
                    </span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <UserGroupIcon className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Products */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">Active Products</p>
                  <p className="text-2xl font-bold text-foreground">{analytics.products.active}</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    {analytics.products.featured} featured
                  </p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                  <ShoppingBagIcon className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Order Status Breakdown */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Order Status Breakdown</CardTitle>
                <CardDescription>Current distribution of order statuses</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(analytics.orders.byStatus).map(([status, count]) => (
                  <div key={status} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${
                        status === 'completed' ? 'bg-green-500' :
                        status === 'pending' ? 'bg-yellow-500' :
                        status === 'cancelled' ? 'bg-red-500' :
                        'bg-blue-500'
                      }`} />
                      <span className="text-sm capitalize">{status.replace(/([A-Z])/g, ' $1')}</span>
                    </div>
                    <Badge variant="secondary">{count}</Badge>
                  </div>
                ))}
              </CardContent>
            </Card>
          </motion.div>

          {/* Top Products */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Products</CardTitle>
                <CardDescription>Products with highest revenue this month</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {analytics.topProducts.map((product, index) => (
                  <div key={product.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-accent/10 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-accent">#{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-medium text-sm">{product.name}</p>
                        <p className="text-xs text-muted-foreground">{product.orders} orders</p>
                      </div>
                    </div>
                    <span className="font-medium text-sm">{formatPrice(product.revenue)}</span>
                  </div>
                ))}
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-8"
        >
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest activities across your platform</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center gap-4 p-3 bg-muted/50 rounded-lg">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      activity.type === 'order' ? 'bg-blue-100' :
                      activity.type === 'user' ? 'bg-green-100' :
                      'bg-purple-100'
                    }`}>
                      {activity.type === 'order' ? (
                        <DocumentTextIcon className="h-4 w-4 text-blue-600" />
                      ) : activity.type === 'user' ? (
                        <UserGroupIcon className="h-4 w-4 text-green-600" />
                      ) : (
                        <ShoppingBagIcon className="h-4 w-4 text-purple-600" />
                      )}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{activity.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(activity.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </RoleBasedLayout>
  );
}
