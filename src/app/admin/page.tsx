'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  ShoppingBagIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  EyeIcon,
  PlusIcon,
  ShieldCheckIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatPrice } from '@/lib/utils';
import { useRequireAdmin } from '@/contexts/auth-context';
import { RoleBasedLayout } from '@/components/layout/role-based-layout';

// Mock data for dashboard (KES pricing)
const dashboardStats = {
  totalRevenue: 1890000, // KES 1.89M
  revenueChange: 12.5,
  totalOrders: 156,
  ordersChange: 8.2,
  totalCustomers: 89,
  customersChange: 15.3,
  averageOrderValue: 12115, // KES 12,115
  aovChange: -2.1
};

const recentOrders = [
  {
    id: 'ORD-001',
    customer: '<PERSON>ku',
    email: '<EMAIL>',
    total: 12500, // KES 12,500
    status: 'processing',
    date: '2024-01-15',
    items: 2
  },
  {
    id: 'ORD-002',
    customer: 'David Kimani',
    email: '<EMAIL>',
    total: 21000, // KES 21,000
    status: 'shipped',
    date: '2024-01-14',
    items: 1
  },
  {
    id: 'ORD-003',
    customer: 'Mary Achieng',
    email: '<EMAIL>',
    total: 33500, // KES 33,500
    status: 'delivered',
    date: '2024-01-13',
    items: 3
  }
];

const topProducts = [
  {
    id: '1',
    name: 'Elegant Portrait - Classic Style',
    sales: 45,
    revenue: 562500, // KES 562,500
    image: '/products/portrait1.jpg'
  },
  {
    id: '2',
    name: 'Glamorous Bedazzled Art',
    sales: 32,
    revenue: 672000, // KES 672,000
    image: '/products/portrait2.jpg'
  },
  {
    id: '3',
    name: 'Custom Family Portrait',
    sales: 28,
    revenue: 11199.72,
    image: '/products/portrait3.jpg'
  }
];

export default function AdminDashboard() {
  const { user, isLoading } = useRequireAdmin();
  const [timeRange, setTimeRange] = useState('30d');

  // Show loading while checking admin access
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center animate-pulse">
            <ShieldCheckIcon className="h-8 w-8 text-white" />
          </div>
          <p className="text-muted-foreground">Verifying admin access...</p>
        </div>
      </div>
    );
  }

  // This will only render if user is admin (useRequireAdmin handles redirects)
  if (!user || user.role !== 'admin') {
    return null;
  }

  const StatCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    prefix = '', 
    suffix = '' 
  }: {
    title: string;
    value: number;
    change: number;
    icon: any;
    prefix?: string;
    suffix?: string;
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {prefix}{typeof value === 'number' && value > 1000 ? formatPrice(value) : value.toLocaleString()}{suffix}
        </div>
        <div className="flex items-center text-xs text-muted-foreground">
          {change > 0 ? (
            <TrendingUpIcon className="h-3 w-3 text-success mr-1" />
          ) : (
            <TrendingDownIcon className="h-3 w-3 text-destructive mr-1" />
          )}
          <span className={change > 0 ? 'text-success' : 'text-destructive'}>
            {Math.abs(change)}%
          </span>
          <span className="ml-1">from last month</span>
        </div>
      </CardContent>
    </Card>
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'secondary';
      case 'processing': return 'warning';
      case 'shipped': return 'default';
      case 'delivered': return 'success';
      case 'cancelled': return 'destructive';
      default: return 'secondary';
    }
  };

  return (
    <RoleBasedLayout requiredRole="admin" showRoleInfo>
      <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="mb-8"
      >
        <div className="flex flex-col gap-6">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
                  <ShieldCheckIcon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground">
                    Admin Dashboard
                  </h1>
                  <p className="text-sm text-muted-foreground">
                    Welcome back, {user?.name || 'Admin'}!
                  </p>
                </div>
              </div>
              <p className="text-muted-foreground text-sm sm:text-base">
                Here's what's happening with your Bedazzled store in Kenya.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-2">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="px-3 py-2 border border-border rounded-md bg-background text-foreground text-sm"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">Last year</option>
              </select>
              <Button className="w-full sm:w-auto">
                <PlusIcon className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Add Product</span>
                <span className="sm:hidden">Add</span>
              </Button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8"
      >
        <StatCard
          title="Total Revenue"
          value={dashboardStats.totalRevenue}
          change={dashboardStats.revenueChange}
          icon={CurrencyDollarIcon}
        />
        <StatCard
          title="Total Orders"
          value={dashboardStats.totalOrders}
          change={dashboardStats.ordersChange}
          icon={ShoppingBagIcon}
        />
        <StatCard
          title="Total Customers"
          value={dashboardStats.totalCustomers}
          change={dashboardStats.customersChange}
          icon={UserGroupIcon}
        />
        <StatCard
          title="Avg. Order Value"
          value={dashboardStats.averageOrderValue}
          change={dashboardStats.aovChange}
          icon={ChartBarIcon}
        />
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
        {/* Recent Orders */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Recent Orders</CardTitle>
              <CardDescription>Latest orders from your customers</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-4 border border-border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-semibold text-sm">{order.id}</span>
                        <Badge variant={getStatusColor(order.status) as any}>
                          {order.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{order.customer}</p>
                      <p className="text-xs text-muted-foreground">{order.email}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-sm font-medium">{formatPrice(order.total)}</span>
                        <span className="text-xs text-muted-foreground">{order.items} items</span>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <EyeIcon className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Button variant="outline" className="w-full">
                  View All Orders
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Top Products */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Top Products</CardTitle>
              <CardDescription>Best performing products this month</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topProducts.map((product, index) => (
                  <div key={product.id} className="flex items-center gap-4 p-4 border border-border rounded-lg">
                    <div className="w-12 h-12 bg-gradient-to-br from-accent/20 to-highlight/20 rounded-lg flex items-center justify-center flex-shrink-0">
                      <span className="text-sm font-bold">#{index + 1}</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-semibold text-sm line-clamp-1">{product.name}</p>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-muted-foreground">{product.sales} sales</span>
                        <span className="text-sm font-medium">{formatPrice(product.revenue)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Button variant="outline" className="w-full">
                  View All Products
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="mt-8"
      >
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks and shortcuts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button variant="outline" className="h-20 flex-col">
                <PlusIcon className="h-6 w-6 mb-2" />
                Add Product
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <ShoppingBagIcon className="h-6 w-6 mb-2" />
                View Orders
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <UserGroupIcon className="h-6 w-6 mb-2" />
                Customers
              </Button>
              <Button variant="outline" className="h-20 flex-col">
                <ChartBarIcon className="h-6 w-6 mb-2" />
                Analytics
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
      </div>
    </RoleBasedLayout>
  );
}
